/* pages/Login/Login.wxss */
page {
    background-color: rgb(223, 223, 223);
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

/* 主容器 */
.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    width: 100%;
    background: transparent;
}

/* 头部区域样式 */
.head {
    padding: 50rpx 0 40rpx 0;
    min-height: 400rpx;
    max-height: 35vh;
    background-color: rgb(18, 150, 219);
    text-align: center;
    color: white;
    font-size: 36rpx;
    font-weight: bolder;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    margin: 0;
}

.logo {
    width: 160rpx;
    height: 160rpx;
    min-width: 120rpx;
    min-height: 120rpx;
    max-width: 200rpx;
    max-height: 200rpx;
    border-radius: 50%;
    background-color: rgb(233, 233, 233);
    flex-shrink: 0;
}

.app-title {
    margin: 20rpx 20rpx 10rpx 20rpx;
    font-size: 40rpx;
    line-height: 1.4;
    font-weight: bold;
}

.app-subtitle {
    margin: 0 20rpx 15rpx 20rpx;
    font-size: 28rpx;
    line-height: 1.4;
    opacity: 0.9;
    font-weight: normal;
}

/* 下方内容区域 - 渐变背景 */
.content-area {
    flex: 1;
    background: linear-gradient(to bottom, #1296DB, #FFFFFF);
    min-height: calc(100vh - 400rpx);
    padding-bottom: 40rpx;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 40rpx;
}

/* 登录表单容器 */
.login-form {
    width: 90%;
    max-width: 600rpx;
    display: flex;
    justify-content: center;
}

/* 表单卡片 */
.form-card {
    width: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;
    padding: 40rpx 30rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10rpx);
    box-sizing: border-box;
}

/* 表单区域 */
.form-section {
    margin-bottom: 30rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 15rpx;
}

/* 复选框样式 */
.checkbox-item {
    display: flex;
    align-items: center;
    padding: 15rpx 0;
}

.checkbox-label {
    margin-left: 15rpx;
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
}

/* 输入框样式 */
.form-input {
    width: 100%;
    height: 80rpx;
    background-color: #f8f9fa;
    border: 2rpx solid #e9ecef;
    border-radius: 12rpx;
    padding: 0 20rpx;
    font-size: 30rpx;
    color: #333;
    box-sizing: border-box;
    transition: all 0.3s ease;
}

.form-input:focus {
    border-color: #1296DB;
    background-color: #fff;
    box-shadow: 0 0 0 4rpx rgba(18, 150, 219, 0.1);
}

.form-input::placeholder {
    color: #999;
    font-size: 28rpx;
}

/* 按钮组样式 */
.button-group {
    display: flex;
    gap: 20rpx;
    margin-top: 40rpx;
    justify-content: space-between;
}

.cancel-btn, .confirm-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 12rpx;
    font-size: 32rpx;
    font-weight: bold;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cancel-btn {
    background-color: #f8f9fa;
    color: #666;
    border: 2rpx solid #e9ecef;
}

.cancel-btn:active {
    background-color: #e9ecef;
}

.confirm-btn {
    background-color: #1296DB !important;
    color: white !important;
    border-color: #1296DB !important;
}

.confirm-btn:active {
    background-color: #0d7bc4 !important;
    border-color: #0d7bc4 !important;
}

/* 覆盖微信小程序默认的复选框样式 */
checkbox .wx-checkbox-input {
    border-radius: 6rpx;
    width: 40rpx;
    height: 40rpx;
    border: 2rpx solid #ddd;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked {
    background-color: #1296DB !important;
    border-color: #1296DB !important;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
    color: white !important;
    font-size: 24rpx;
}

/* 响应式设计 - 小屏幕适配 */
@media (max-width: 400px) {
    .head {
        min-height: 350rpx;
        padding: 40rpx 0 30rpx 0;
        font-size: 32rpx;
    }

    .content-area {
        min-height: calc(100vh - 350rpx);
        padding-top: 30rpx;
    }

    .logo {
        width: 140rpx;
        height: 140rpx;
    }

    .app-title {
        font-size: 36rpx;
        margin: 15rpx 15rpx 8rpx 15rpx;
    }

    .app-subtitle {
        font-size: 26rpx;
        margin: 0 15rpx 12rpx 15rpx;
    }

    .login-form {
        width: 95%;
    }

    .form-card {
        padding: 30rpx 25rpx;
    }

    .section-title {
        font-size: 30rpx;
    }

    .form-input {
        height: 75rpx;
        font-size: 28rpx;
    }

    .cancel-btn, .confirm-btn {
        height: 75rpx;
        font-size: 30rpx;
    }
}

/* 响应式设计 - 大屏幕适配 */
@media (min-width: 800px) {
    .head {
        max-height: 30vh;
        min-height: 450rpx;
        padding: 60rpx 0 50rpx 0;
        font-size: 40rpx;
    }

    .content-area {
        min-height: calc(100vh - 450rpx);
        padding-top: 60rpx;
    }

    .logo {
        width: 180rpx;
        height: 180rpx;
    }

    .app-title {
        font-size: 44rpx;
        margin: 25rpx 25rpx 12rpx 25rpx;
    }

    .app-subtitle {
        font-size: 30rpx;
        margin: 0 25rpx 18rpx 25rpx;
    }

    .login-form {
        max-width: 500rpx;
    }

    .form-card {
        padding: 50rpx 40rpx;
    }
}
  
  
<!--pages/Login/Login.wxml-->
<view class="container">
    <!-- 头部区域 -->
    <view class="head">
        <image class="logo" src="../../static/images/logo.png" mode="aspectFit" />
        <view class="app-title">
            <text>系统登录</text>
        </view>
        <view class="app-subtitle">
            <text>请输入您的登录信息</text>
        </view>
    </view>

    <!-- 下方内容区域，应用渐变背景 -->
    <view class="content-area">
        <form bindsubmit="formSubmit">
            <view wx:if="{{isShow}}" class='login-form'>
                <!-- 登录表单卡片 -->
                <view class='form-card'>
                    <!-- 用户类型选择 -->
                    <view class="form-section">
                        <view class="section-title">用户类型</view>
                        <checkbox-group bindchange="checkboxChange">
                            <view class="checkbox-item">
                                <checkbox value="driver" checked="true" />
                                <text class="checkbox-label">司机</text>
                            </view>
                        </checkbox-group>
                    </view>

                    <!-- 账号输入 -->
                    <view class="form-section">
                        <view class="section-title">账号</view>
                        <input class="form-input" name="userId" type="text" placeholder="请输入账号" bindinput="bindKeyInput" value="{{student_info.userId}}" />
                    </view>

                    <!-- 电话号码输入 -->
                    <view class="form-section">
                        <view class="section-title">电话号码</view>
                        <input class="form-input" name="Phone" type="number" placeholder="请输入电话号码" bindinput="bindKeyInput" value="{{student_info.Phone}}" />
                    </view>

                    <!-- 按钮组 -->
                    <view class='button-group'>
                        <button type="default" class="cancel-btn" catchtap="hideCover">取消</button>
                        <button type="primary" class="confirm-btn" form-type='submit' bindtap="openNews">确定</button>
                    </view>
                </view>
            </view>
        </form>
    </view>
</view>
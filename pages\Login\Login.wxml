<!--pages/Login/Login.wxml-->
<view class="container">
    <!-- 头部区域 -->
    <view class="head">
        <view class="app-title">
            <text>用户登录</text>
        </view>
    </view>

    <!-- 下方内容区域，应用渐变背景 -->
    <view class="content-area">
        <form bindsubmit="formSubmit">
            <view wx:if="{{isShow}}" class='login-form'>
                <!-- 登录表单卡片 -->
                <view class='form-card'>
                    <!-- 位置权限提示 -->
                    <view class="form-section">
                        <view class="section-title">位置权限</view>
                        <view class="location-permission-info">
                            <text class="permission-desc">为了记录反馈位置信息，需要获取您的位置权限</text>
                            <view class="permission-status {{locationPermissionStatus}}">
                                <text wx:if="{{locationPermissionStatus === 'unknown'}}">📍 点击登录时将请求位置权限</text>
                                <text wx:if="{{locationPermissionStatus === 'granted'}}">✅ 位置权限已授权</text>
                                <text wx:if="{{locationPermissionStatus === 'denied'}}">❌ 位置权限被拒绝，请在设置中开启</text>
                            </view>
                        </view>
                    </view>

                    <!-- 用户类型选择 -->
                    <view class="form-section">
                        <view class="section-title">用户类型</view>
                        <checkbox-group bindchange="checkboxChange">
                            <view class="checkbox-item">
                                <checkbox value="driver" checked="true" />
                                <text class="checkbox-label">司机</text>
                            </view>
                        </checkbox-group>
                    </view>

                    <!-- 账号输入 -->
                    <view class="form-section">
                        <view class="section-title">账号</view>
                        <input class="form-input" name="userId" type="text" placeholder="请输入账号" bindinput="bindKeyInput" value="{{student_info.userId}}" />
                    </view>

                    <!-- 电话号码输入 -->
                    <view class="form-section">
                        <view class="section-title">电话号码</view>
                        <input class="form-input" name="Phone" type="number" placeholder="请输入电话号码" bindinput="bindKeyInput" value="{{student_info.Phone}}" />
                    </view>

                    <!-- 按钮组 -->
                    <view class='button-group'>
                        <button type="default" class="cancel-btn" catchtap="hideCover">取消</button>
                        <button type="primary" class="confirm-btn" form-type='submit' bindtap="openNews">确定</button>
                    </view>
                </view>
            </view>
        </form>
    </view>
</view>
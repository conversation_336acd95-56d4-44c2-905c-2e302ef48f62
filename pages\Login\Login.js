const requestModule = require('../../request/common');
const http = require('../../utils/http');
const utils = require('../../utils/util');
const common = require('../../config/common');
Page({

    /**
     * 页面的初始数据
     */
    data: {
        // student_info用于存储弹框中输入的信息
        user_info: {},
        // isShow用于控制弹窗的显示与否 默认显示弹窗
        isShow: true,
        driver:true
    },
    //实时获取弹窗的输入的值
    bindKeyInput(e) {
        // console.log(e.detail.value)
    },
    openNews(){
        wx.requestSubscribeMessage({
          tmplIds: ['ZWAE_ImGeFMeTdwACSYle-oaGz96D1R96FFMue0toss'],
          success (res) {}
        })
      },
    // 隐藏弹窗函数
    hideCover() {
        this.setData({
            isShow: false
        })
    },
    checkboxChange(e){
        console.log(e.detail.value);
        if(e.detail.value[0])
        {
            this.data.driver=true;
        }
        else
        {
            this.data.driver=false;
        }
        console.log(this.data.driver)
    },
    // 展示弹窗函数
    showCover() {
        this.setData({
            isShow: true
        })
    },
    // 点击确定后的事件处理 获取确认后的用户信息 并作出相应的处理
    formSubmit: function (e) {

        // 获取输入的用户ID
        let userId = e.detail.value.userId;
        // 获取输入的电话号码
        let Phone = e.detail.value.Phone;
        let driver = this.data.driver;
        let RolesId = "";
        if(driver==true)
        {
            RolesId=common.SJRoleId;
        }
        // console.log(name+ID+college+major);
        utils.getStorage('OpenId').then(stores => {
            http(requestModule.BandOpenIdToMembership, {
                data: {
                    PersonId: userId,
                    Phone: Phone,
                    RolesId,
                }
            }).then(
                res => {
                    if (res.data.status == "suc") {

                        let tempdata = res.data.data;
                        tempdata.userId = userId;
                        tempdata.Phone = Phone;
                        tempdata.openId = stores;
                        wx.showModal({
                            title: '温馨提示',
                            content: '授权微信登录后才能正常使用小程序功能',
                            success(res) {
                                if (res.confirm) {
                                    utils.setStorage('userInfo', tempdata).then(res => {
                                        // 更新全局登录状态
                                        const app = getApp();
                                        app.globalData.isLoggedIn = true;
                                        app.globalData.userInfo = tempdata;
                                        console.log("登录成功，已更新全局状态");

                                        wx.redirectTo({
                                            url: '../index/index',
                                        })
                                    })
                                }
                            },
                        })
                    }
                    else {
                        wx.showModal({
                            title: '警告',
                            content: res.data.errorMsg,
                            showCancel: false, // 是否显示取消按钮，设为false表示不显示
                            confirmText: '确认', // 确认按钮的文本内容
                        })
                    }
                }
            ).catch(
                err => {

                    console.log("原因", err);
                    wx.showModal({
                        title: '通讯失败',
                        content: "请重新登陆！",
                        showCancel: false, // 是否显示取消按钮，设为false表示不显示
                        confirmText: '确认', // 确认按钮的文本内容
                        success(res) {
                            if (res.confirm) {
                                // // 用户点击确认按钮后的操作
                                // console.log('用户点击了确认按钮');
                            }
                        }
                    })
                }
            )
        }).catch(err=>{
            wx.redirectTo({
                url: '../index/index',
            })
        })
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad() {
        // 检查是否有OpenId，没有则自动获取
        const openId = wx.getStorageSync('OpenId');
        if (!openId) {
            console.log('没有OpenId，自动获取');
            this.getOpenId();
        }
    },

    // 获取OpenId
    getOpenId() {
        const utils = require('../../utils/util');
        const user = require('../../request/user');

        utils.wxLogin().then(res => {
            user.loginWithoutRedirect(res);
        }).catch(err => {
            console.log('获取OpenId失败:', err);
            wx.showToast({
                title: '初始化失败，请重试',
                icon: 'none'
            });
        });
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        // 隐藏可能存在的加载提示
        wx.hideLoading();
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})

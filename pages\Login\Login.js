const requestModule = require('../../request/common');
const http = require('../../utils/http');
const utils = require('../../utils/util');
const common = require('../../config/common');
const LocationPermissionManager = require('../../utils/location-permission-manager');
Page({

    /**
     * 页面的初始数据
     */
    data: {
        // student_info用于存储表单中输入的信息
        student_info: {
            userId: '',
            Phone: ''
        },
        // isShow用于控制登录表单的显示与否 默认显示
        isShow: true,
        driver: true,
        // 位置权限状态：unknown, granted, denied
        locationPermissionStatus: 'unknown'
    },
    //实时获取表单输入的值
    bindKeyInput(e) {
        const field = e.currentTarget.dataset.field || e.target.dataset.field;
        const value = e.detail.value;

        // 根据输入框的name属性更新对应的数据
        if (e.target.name === 'userId') {
            this.setData({
                'student_info.userId': value
            });
        } else if (e.target.name === 'Phone') {
            this.setData({
                'student_info.Phone': value
            });
        }

        console.log('输入值更新:', this.data.student_info);
    },
    openNews(){
        wx.requestSubscribeMessage({
          tmplIds: ['ZWAE_ImGeFMeTdwACSYle-oaGz96D1R96FFMue0toss'],
          success (res) {}
        })
      },
    // 取消登录函数
    hideCover() {
        // 可以选择返回上一页或者清空表单
        wx.showModal({
            title: '确认取消',
            content: '确定要取消登录吗？',
            success: (res) => {
                if (res.confirm) {
                    // 清空表单数据
                    this.setData({
                        'student_info.userId': '',
                        'student_info.Phone': '',
                        driver: true
                    });
                    // 或者可以选择跳转到其他页面
                    // wx.navigateBack();
                }
            }
        });
    },
    checkboxChange(e){
        console.log(e.detail.value);
        if(e.detail.value[0])
        {
            this.data.driver=true;
        }
        else
        {
            this.data.driver=false;
        }
        console.log(this.data.driver)
    },
    // 展示弹窗函数
    showCover() {
        this.setData({
            isShow: true
        })
    },

    // 检查位置权限状态
    async checkLocationPermission() {
        try {
            const permissionStatus = await LocationPermissionManager.checkLocationPermission();

            if (permissionStatus.hasPermission) {
                this.setData({
                    locationPermissionStatus: 'granted'
                });
            } else if (permissionStatus.isDenied) {
                this.setData({
                    locationPermissionStatus: 'denied'
                });
            } else {
                this.setData({
                    locationPermissionStatus: 'unknown'
                });
            }

            return permissionStatus;
        } catch (error) {
            console.error('检查位置权限失败:', error);
            this.setData({
                locationPermissionStatus: 'unknown'
            });
            return { hasPermission: false, isDenied: false, isUnknown: true };
        }
    },

    // 请求位置权限
    async requestLocationPermission() {
        try {
            const result = await LocationPermissionManager.checkAndRequestLocationPermission();

            if (result.success && result.hasPermission) {
                this.setData({
                    locationPermissionStatus: 'granted'
                });
                return true;
            } else {
                this.setData({
                    locationPermissionStatus: 'denied'
                });

                // 显示权限被拒绝的提示
                wx.showModal({
                    title: '位置权限被拒绝',
                    content: '为了记录反馈位置信息，需要位置权限。请在设置中开启位置权限后重新登录。',
                    confirmText: '去设置',
                    cancelText: '取消',
                    success: (res) => {
                        if (res.confirm) {
                            wx.openSetting({
                                success: (settingRes) => {
                                    if (settingRes.authSetting['scope.userLocation']) {
                                        this.setData({
                                            locationPermissionStatus: 'granted'
                                        });
                                        wx.showToast({
                                            title: '权限开启成功',
                                            icon: 'success'
                                        });
                                    }
                                }
                            });
                        }
                    }
                });

                return false;
            }
        } catch (error) {
            console.error('请求位置权限失败:', error);
            this.setData({
                locationPermissionStatus: 'denied'
            });
            return false;
        }
    },

    // 点击确定后的事件处理 获取确认后的用户信息 并作出相应的处理
    formSubmit: async function (e) {
        // 获取输入的用户ID
        let userId = e.detail.value.userId;
        // 获取输入的电话号码
        let Phone = e.detail.value.Phone;

        // 表单验证
        if (!userId || userId.trim() === '') {
            wx.showToast({
                title: '请输入账号',
                icon: 'none',
                duration: 2000
            });
            return;
        }

        if (!Phone || Phone.trim() === '') {
            wx.showToast({
                title: '请输入电话号码',
                icon: 'none',
                duration: 2000
            });
            return;
        }

        // 验证电话号码格式（简单验证）
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(Phone)) {
            wx.showToast({
                title: '请输入正确的手机号码',
                icon: 'none',
                duration: 2000
            });
            return;
        }

        // 检查并请求位置权限
        const hasLocationPermission = await this.requestLocationPermission();
        if (!hasLocationPermission) {
            wx.showToast({
                title: '需要位置权限才能登录',
                icon: 'none',
                duration: 2000
            });
            return;
        }

        // 显示加载提示
        wx.showLoading({
            title: '登录中...',
            mask: true
        });
        let driver = this.data.driver;
        let RolesId = "";
        if(driver==true)
        {
            RolesId=common.SJRoleId;
        }
        // console.log(name+ID+college+major);
        utils.getStorage('OpenId').then(stores => {
            http(requestModule.BandOpenIdToMembership, {
                data: {
                    PersonId: userId,
                    Phone: Phone,
                    RolesId,
                }
            }).then(
                res => {
                    // 隐藏加载提示
                    wx.hideLoading();

                    if (res.data.status == "suc") {
                        let tempdata = res.data.data;
                        tempdata.userId = userId;
                        tempdata.Phone = Phone;
                        tempdata.openId = stores;

                        wx.showModal({
                            title: '登录成功',
                            content: '欢迎使用系统！',
                            showCancel: false,
                            confirmText: '确认',
                            success(modalRes) {
                                if (modalRes.confirm) {
                                    utils.setStorage('userInfo', tempdata).then(() => {
                                        // 更新全局登录状态
                                        const app = getApp();
                                        app.globalData.isLoggedIn = true;
                                        app.globalData.userInfo = tempdata;
                                        console.log("登录成功，已更新全局状态");

                                        wx.redirectTo({
                                            url: '../index/index',
                                        })
                                    }).catch(storageErr => {
                                        console.error('存储用户信息失败:', storageErr);
                                        wx.showToast({
                                            title: '登录失败，请重试',
                                            icon: 'none'
                                        });
                                    });
                                }
                            },
                        })
                    }
                    else {
                        wx.showModal({
                            title: '登录失败',
                            content: res.data.errorMsg || '登录信息验证失败，请检查账号和电话号码',
                            showCancel: false,
                            confirmText: '确认',
                        })
                    }
                }
            ).catch(
                err => {
                    // 隐藏加载提示
                    wx.hideLoading();

                    console.log("网络请求失败原因:", err);
                    wx.showModal({
                        title: '网络错误',
                        content: "网络连接失败，请检查网络后重试！",
                        showCancel: false,
                        confirmText: '确认',
                        success(res) {
                            if (res.confirm) {
                                console.log('用户确认网络错误提示');
                            }
                        }
                    })
                }
            )
        }).catch(storageErr => {
            // 隐藏加载提示
            wx.hideLoading();

            console.log("获取OpenId失败:", storageErr);
            wx.showToast({
                title: '初始化失败，请重试',
                icon: 'none',
                duration: 2000
            });
        })
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad() {
        // 检查是否有OpenId，没有则自动获取
        const openId = wx.getStorageSync('OpenId');
        if (!openId) {
            console.log('没有OpenId，自动获取');
            this.getOpenId();
        }

        // 检查位置权限状态
        this.checkLocationPermission();
    },

    // 获取OpenId
    getOpenId() {
        const utils = require('../../utils/util');
        const user = require('../../request/user');

        utils.wxLogin().then(res => {
            user.loginWithoutRedirect(res);
        }).catch(err => {
            console.log('获取OpenId失败:', err);
            wx.showToast({
                title: '初始化失败，请重试',
                icon: 'none'
            });
        });
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        // 隐藏可能存在的加载提示
        wx.hideLoading();
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})
